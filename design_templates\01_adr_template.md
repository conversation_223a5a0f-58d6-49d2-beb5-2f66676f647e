# ADR-XXX: [簡短的決策標題]

---

**狀態 (Status):** `[例如：提議中 (Proposed), 已接受 (Accepted), 已取代 (Superseded by ADR-YYY), 已棄用 (Deprecated)]`

**決策者 (Deciders):** `[列出參與決策的主要人員或團隊]`

**日期 (Date):** `YYYY-MM-DD`

---

## 1. 背景與問題陳述 (Context and Problem Statement)

*   `[描述需要做出此架構決策的技術背景、上下文環境。]`
*   `[清晰地陳述面臨的具體問題或需要解決的架構挑戰是什麼？這個問題會導致什麼後果？]`
*   `[是否有任何約束條件或先前已做出的相關決策需要在此處說明？]`

## 2. 考量的選項 (Considered Options)

*   `[列出所有被認真考慮過的解決方案/選項。對每個選項進行簡要描述。]`

### 選項一：[選項 1 的名稱或簡述]
*   **描述：** `[詳細描述此選項的實現方式、主要思路。]`
*   **優點 (Pros)：**
    *   `[優點 1]`
    *   `[優點 2]`
    *   `...`
*   **缺點 (Cons)：**
    *   `[缺點 1]`
    *   `[缺點 2]`
    *   `...`

### 選項二：[選項 2 的名稱或簡述]
*   **描述：** `[...]`
*   **優點 (Pros)：**
    *   `[...]`
*   **缺點 (Cons)：**
    *   `[...]`

### (選填) 選項三：[選項 3 的名稱或簡述]
*   `... (以此類推)`

## 3. 決策 (Decision Outcome)

**最終選擇的方案：** `[明確指出最終被接受的選項是哪一個，例如："選項一：使用 PostgreSQL 作為主要資料庫"。]`

**選擇理由 (Rationale):**
*   `[詳細闡述選擇此方案的主要原因。它是如何更好地解決問題的？與其他選項相比，其關鍵優勢是什麼？]`
*   `[可以從技術契合度、成本、風險、團隊熟悉度、未來擴展性等多個維度進行說明。]`
*   `[說明此決策如何權衡了不同選項的優缺點。]`

## 4. 決策的後果與影響 (Consequences)

*   **正面影響 / 預期收益：**
    *   `[此決策預期會帶來哪些積極的結果或收益？]`
*   **負面影響 / 需要注意的風險：**
    *   `[此決策可能帶來哪些潛在的負面影響、新的風險或需要額外處理的折衷？]`
    *   `[例如：增加了學習成本、引入了新的依賴、對現有系統的兼容性問題等。]`
*   **對其他組件/團隊的影響：**
    *   `[此決策是否會影響專案的其他部分或依賴此系統的其他團隊？如何協調？]`
*   **未來可能需要重新評估的觸發條件：**
    *   `[在什麼情況下（例如：用戶量達到某個閾值、出現新的技術等），這個決策可能需要被重新審視？]`

## 5. (選填) 執行計畫概要 (Implementation Plan Outline)

*   `[簡要列出執行此決策所需的主要步驟或里程碑 (如果適用)。]`

## 6. (選填) 相關參考 (References)

*   `[列出與此決策相關的任何外部文檔、研究報告、RFC、Issue Tracker 連結等。]`

---
**ADR 審核與簽署 (Sign-off - 選填):**

| 角色             | 姓名/團隊 | 簽署日期   | 備註     |
| :--------------- | :-------- | :--------- | :------- |
| 主要架構師     |           | YYYY-MM-DD |          |
| 技術負責人     |           | YYYY-MM-DD |          |
| (其他相關利益方) |           | YYYY-MM-DD |          | 