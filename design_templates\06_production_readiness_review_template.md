# 生產就緒審查 (Production Readiness Review - PRR) - [服務/專案名稱]

---

**審查對象 (Service/Project Name):** `[請填寫]`

**版本 (Version being reviewed):** `[例如：v1.2.0, 或特定 Git Commit SHA]`

**PRR 會議日期 (PRR Meeting Date):** `YYYY-MM-DD`

**主要負責人/團隊 (Lead/Team):** `[開發團隊負責人、主要工程師]`

**審查委員會/參與者 (Review Board/Attendees):** `[SRE 團隊代表、架構師、安全團隊代表、運維團隊代表、(若適用)產品經理、(若適用)利害關係人]`

**相關設計文檔 (Links to Key Documents):**
*   系統架構文檔 (SA): `[連結]`
*   系統詳細設計文檔 (SD): `[連結]`
*   API 設計規範 (若有): `[連結]`
*   安全與隱私審查報告: `[連結]`
*   測試報告/覆蓋率: `[連結]`
*   部署計畫: `[連結]`
*   回滾計畫: `[連結]`
*   監控儀表板 (Dashboard) 預覽: `[連結]`
*   告警配置預覽: `[連結]`
*   Runbook/Playbook (運維手冊) 草稿: `[連結]`

---

## 1. 服務/專案概述 (Service/Project Overview)

*   **核心功能與業務價值:** `[簡要重述此服務/專案的核心功能及其為業務或用戶帶來的價值。]`
*   **本次發布的變更摘要:** `[如果是針對現有服務的更新，簡要說明本次發布包含的主要變更、新功能或修復。]`
*   **目標上線日期:** `YYYY-MM-DD`

## 2. 架構與設計回顧 (Architecture and Design Review)
*   `[ ]` **架構符合性:** 當前實現是否與已批准的系統架構設計 (SA) 一致？若有偏差，是否已記錄並獲得批准 (例如，通過 ADR)？
*   `[ ]` **設計文檔完整性與更新:** 相關的 SD 文檔是否已更新以反映最終實現？是否清晰、準確？
*   `[ ]` **依賴關係管理:** 是否清晰地記錄了所有外部和內部服務依賴？依賴服務的 SLA 是否已知並符合本服務需求？是否有處理依賴服務故障的機制？
*   `[ ]` **API 契約 (若適用):** API 是否遵循了團隊的設計規範？是否向後兼容？是否有版本控制？

## 3. 可靠性與彈性 (Reliability and Resilience)

*   `[ ]` **服務水平目標 (SLO):** 是否為此服務定義了明確的 SLOs (例如：可用性、延遲、錯誤率)？這些 SLOs 是否可衡量？
*   `[ ]` **單點故障 (SPOF):** 是否識別並消除了所有潛在的單點故障？
*   `[ ]` **容錯機制:** 是否實施了適當的容錯機制 (例如：重試、超時、斷路器、冪等性設計)？
*   `[ ]` **數據備份與恢復:** (若涉及數據儲存) 是否有可靠的數據備份策略？數據恢復流程是否已測試並記錄在 Runbook 中？RPO 和 RTO 是否滿足業務需求？
*   `[ ]` **災難恢復 (DR - 若適用):** 是否有跨區域/跨數據中心的災難恢復計畫？是否進行過演練？
*   `[ ]` **負載測試與壓力測試:** 是否進行了充分的負載測試和壓力測試，以驗證系統在高負載下的表現和容量上限？測試結果是否達到預期？

## 4. 可擴展性 (Scalability)

*   `[ ]` **擴展策略:** 是否有清晰的水平擴展和/或垂直擴展策略？擴展過程是否可以自動化？
*   `[ ]` **容量規劃:** 是否基於預期的負載（例如：QPS、用戶數、數據量）進行了容量規劃？目前的資源配置是否足夠？
*   `[ ]` **瓶頸分析:** 是否識別了系統中潛在的性能瓶頸和擴展限制？是否有應對計劃？

## 5. 可觀測性 (Observability) - 日誌、指標、追蹤、告警

*   `[ ]` **日誌 (Logging):**
    *   是否記錄了足夠的、結構化的應用程式日誌和系統日誌，以便於故障排除和審計？
    *   日誌是否包含請求 ID 或 Correlation ID 以便於追蹤跨服務的請求？
    *   日誌是否集中收集和管理？是否避免記錄敏感資訊？
*   `[ ]` **指標 (Metrics):**
    *   是否定義並暴露了關鍵的業務指標和系統性能指標 (例如：請求計數、錯誤率、延遲 (平均值, P95, P99)、資源使用率、隊列長度)？
    *   指標是否接入了監控系統 (例如：Prometheus)？
*   `[ ]` **追蹤 (Tracing):**
    *   是否實現了分散式追蹤，以便於分析跨多個服務的請求路徑和延遲？
*   `[ ]` **儀表板 (Dashboards):**
    *   是否創建了包含關鍵指標的監控儀表板 (例如：使用 Grafana)，以便於實時了解服務狀態？
*   `[ ]` **告警 (Alerting):**
    *   是否針對關鍵指標和錯誤情況配置了實時告警？
    *   告警閾值是否合理？告警通知是否能及時送達給相關負責人 (On-call)？
    *   是否有針對告警的處理流程或 Playbook？

## 6. 部署與配置管理 (Deployment and Configuration Management)

*   `[ ]` **自動化部署 (Automated Deployment):** 部署流程是否已完全自動化 (例如：通過 CI/CD pipeline)？
*   `[ ]` **部署策略:** 採用何種部署策略 (例如：藍綠部署、金絲雀發布、滾動更新)？是否有零停機部署能力？
*   `[ ]` **配置管理:** 應用程式配置 (例如：資料庫連接字串、API Keys、特性開關) 是否外部化管理？是否支持不同環境 (開發、測試、生產) 的配置？
*   `[ ]` **回滾計畫 (Rollback Plan):** 如果部署失敗或上線後出現嚴重問題，是否有清晰、經過測試的回滾計畫？回滾過程是否快速且可靠？
*   `[ ]` **基礎設施即代碼 (IaC):** 相關的基礎設施是否使用 IaC 工具 (例如：Terraform, Ansible) 進行管理？

## 7. 安全性 (Security)

*   `[ ]` **安全審查:** 是否已完成安全設計審查和程式碼安全審查？所有發現的問題是否已解決或有緩解計劃？ (參考 `05_security_privacy_review_checklist_template.md`)
*   `[ ]` **漏洞掃描與滲透測試:** 是否已進行漏洞掃描？是否計劃或已完成滲透測試 (根據風險級別)？
*   `[ ]` **機密管理:** 所有機密 (密碼、API Keys、證書) 是否都已通過安全的方式管理 (例如：Vault, Secrets Manager)？
*   `[ ]` **最小權限:** 服務運行的帳戶和權限是否遵循最小權限原則？
*   `[ ]` **依賴庫安全:** 第三方依賴庫是否進行了安全掃描並無已知高危漏洞？

## 8. 運維與支持 (Operations and Support)

*   `[ ]` **Runbook/Playbook (運維手冊):** 是否編寫了清晰的運維手冊，涵蓋常見問題的故障排除步驟、緊急應變流程、服務啟停步驟、日常維護任務等？
*   `[ ]` **On-call 準備:** On-call 團隊是否已接受培訓？是否熟悉服務架構、監控儀表板和運維手冊？
*   `[ ]` **文檔完整性:** 開發者文檔、API 文檔 (若有)、用戶文檔 (若有) 是否已準備就緒並更新？
*   `[ ]` **依賴服務的SLA和聯繫方式:** 是否記錄了所有關鍵依賴服務的SLA以及其技術支持聯繫方式？
*   `[ ]` **成本考量:** 服務的預期運行成本是否在預算範圍內？是否有成本優化措施？

## 9. 測試覆蓋 (Test Coverage)

*   `[ ]` **單元測試:** 單元測試覆蓋率是否達到團隊標準？
*   `[ ]` **整合測試:** 是否編寫了足夠的整合測試來驗證組件間的交互？
*   `[ ]` **端到端測試 (E2E Tests - 若適用):** 是否有自動化的端到端測試覆蓋關鍵用戶場景？
*   `[ ]` **性能測試:** (見 3. 可靠性與彈性)
*   `[ ]` **安全測試:** (見 7. 安全性)
*   `[ ]` **所有測試是否在 CI 中自動運行並通過？**

## 10. 發布溝通與 Launch Plan (Release Communication and Launch Plan)

*   `[ ]` **內部溝通:** 是否已通知所有相關的內部團隊 (例如：SRE, 客戶支持, 市場) 關於本次發布的計劃和潛在影響？
*   `[ ]` **外部溝通 (若適用):** 如果是面向用戶的發布，是否有對外溝通計劃 (例如：發布說明、博客、郵件通知)？
*   `[ ]` **Launch Go/No-Go 標準:** 是否定義了明確的 Launch Go/No-Go 檢查點和決策流程？
    *   *例如：所有 P0 級別的 Bug 已修復。*
    *   *例如：關鍵監控指標和告警已配置並驗證。*
    *   *例如：回滾計畫已演練。*
    *   *例如：所有必要的審批 (安全、法務、SRE) 已獲得。*

---

## PRR 審查結論 (PRR Conclusion)

*   **Go / No-Go 決策:** `[Go / No-Go / Go with Conditions]`
*   **決策理由:** `[簡要說明做出此決策的主要原因。]`
*   **附加條件 (Conditions for Go - 若適用):**
    *   `[如果是有條件的 Go，列出必須在上線前或上線後短期內完成的附加條件。]`
*   **主要風險點 (Key Risks Accepted - 若有):**
    *   `[列出審查後仍存在且團隊決定接受的風險點。]`
*   **後續行動項 (Follow-up Action Items):**
    *   `[列出需要在上線後追蹤的行動項、負責人和截止日期。]`

---
**PRR 簽署 (PRR Sign-off):**

| 角色/團隊代表        | 姓名        | 簽署 (同意/不同意/有條件同意) | 備註/條件                                   |
| :------------------- | :---------- | :-------------------------- | :---------------------------------------- |
| 開發團隊負責人       |             |                             |                                           |
| SRE 代表             |             |                             |                                           |
| 安全團隊代表         |             |                             |                                           |
| (其他關鍵審查者)     |             |                             |                                           |
| **最終批准 (Sponsor/Lead):** |             |                             | **(例如：VP of Engineering, Director)** | 